/**
 * Performance Dashboard UI Component
 * Renders the performance dashboard using the mini-framework
 */

import { h } from '../../mini-framework/src/framework.js';

export function renderPerformanceDashboard(performanceData, options = {}) {
  if (!performanceData || !options.visible) {
    return null;
  }

  const {
    currentFPS,
    averageFPS,
    minFPS,
    maxFPS,
    frameDrops,
    frameDropRate,
    totalFrames,
    avgFrameTime,
    isWarning,
    isCritical,
    targetFPS
  } = performanceData;

  const position = options.position || 'top-right';
  const compact = options.compact || false;

  // Determine status color
  let statusColor = '#4CAF50'; // Green - good performance
  if (isWarning && !isCritical) {
    statusColor = '#FF9800'; // Orange - warning
  } else if (isCritical) {
    statusColor = '#F44336'; // Red - critical
  }

  // Position classes
  const positionClasses = {
    'top-left': 'perf-dashboard perf-dashboard--top-left',
    'top-right': 'perf-dashboard perf-dashboard--top-right',
    'bottom-left': 'perf-dashboard perf-dashboard--bottom-left',
    'bottom-right': 'perf-dashboard perf-dashboard--bottom-right'
  };

  const dashboardClass = positionClasses[position] || positionClasses['top-right'];

  if (compact) {
    return h('div', { 
      class: `${dashboardClass} perf-dashboard--compact`,
      style: `border-left: 4px solid ${statusColor}`
    }, [
      h('div', { class: 'perf-row' }, [
        h('span', { class: 'perf-label' }, 'FPS:'),
        h('span', { 
          class: 'perf-value',
          style: `color: ${statusColor}`
        }, Math.round(currentFPS))
      ]),
      isWarning && h('div', { 
        class: 'perf-warning',
        style: `color: ${statusColor}`
      }, isCritical ? '⚠ CRITICAL' : '⚠ WARNING')
    ]);
  }

  return h('div', { 
    class: dashboardClass,
    style: `border-left: 4px solid ${statusColor}`
  }, [
    h('div', { class: 'perf-header' }, [
      h('span', { class: 'perf-title' }, 'Performance'),
      h('span', { 
        class: 'perf-status',
        style: `color: ${statusColor}`
      }, isCritical ? 'CRITICAL' : isWarning ? 'WARNING' : 'GOOD')
    ]),
    
    h('div', { class: 'perf-metrics' }, [
      h('div', { class: 'perf-row' }, [
        h('span', { class: 'perf-label' }, 'Current FPS:'),
        h('span', { 
          class: 'perf-value perf-value--primary',
          style: `color: ${statusColor}`
        }, Math.round(currentFPS))
      ]),
      
      h('div', { class: 'perf-row' }, [
        h('span', { class: 'perf-label' }, 'Target FPS:'),
        h('span', { class: 'perf-value' }, targetFPS)
      ]),
      
      h('div', { class: 'perf-row' }, [
        h('span', { class: 'perf-label' }, 'Average FPS:'),
        h('span', { class: 'perf-value' }, averageFPS)
      ]),
      
      h('div', { class: 'perf-row' }, [
        h('span', { class: 'perf-label' }, 'Min/Max FPS:'),
        h('span', { class: 'perf-value' }, `${minFPS}/${maxFPS}`)
      ]),
      
      h('div', { class: 'perf-row' }, [
        h('span', { class: 'perf-label' }, 'Frame Time:'),
        h('span', { class: 'perf-value' }, `${avgFrameTime}ms`)
      ]),
      
      h('div', { class: 'perf-row' }, [
        h('span', { class: 'perf-label' }, 'Frame Drops:'),
        h('span', { 
          class: 'perf-value',
          style: frameDrops > 0 ? 'color: #FF9800' : ''
        }, `${frameDrops} (${frameDropRate}%)`)
      ]),
      
      h('div', { class: 'perf-row' }, [
        h('span', { class: 'perf-label' }, 'Total Frames:'),
        h('span', { class: 'perf-value' }, totalFrames)
      ])
    ]),
    
    // Warning message
    (isWarning || isCritical) && h('div', { 
      class: 'perf-warning',
      style: `background-color: ${statusColor}20; border-color: ${statusColor}; color: ${statusColor}`
    }, [
      h('strong', {}, isCritical ? '⚠ CRITICAL: ' : '⚠ WARNING: '),
      isCritical 
        ? 'Severe performance issues detected!'
        : 'Performance degradation detected!'
    ])
  ]);
}

export function renderPerformanceToggle(isVisible, onToggle) {
  return h('div', { 
    class: 'perf-toggle',
    onclick: onToggle
  }, [
    h('span', {}, isVisible ? '📊 Hide Stats' : '📊 Show Stats')
  ]);
}
