/**
 * Performance Dashboard
 * Tracks and displays FPS, frame drops, and performance warnings in real time
 */

export class PerformanceDashboard {
  constructor(options = {}) {
    this.targetFPS = options.targetFPS || 60;
    this.frameDropThreshold = options.frameDropThreshold || 0.8; // 80% of target FPS
    this.warningThreshold = options.warningThreshold || 0.9; // 90% of target FPS
    this.historySize = options.historySize || 60; // Keep 60 frames of history
    
    // Performance tracking
    this.fpsHistory = [];
    this.frameDrops = 0;
    this.totalFrames = 0;
    this.averageFPS = 0;
    this.minFPS = Infinity;
    this.maxFPS = 0;
    this.lastFrameTime = 0;
    this.frameTimes = [];
    
    // Warning states
    this.isWarning = false;
    this.isCritical = false;
    this.warningStartTime = 0;
    this.criticalStartTime = 0;
    
    // Dashboard visibility
    this.isVisible = options.visible !== false;
    this.position = options.position || 'top-right';
    
    this.reset();
  }

  reset() {
    this.fpsHistory = [];
    this.frameDrops = 0;
    this.totalFrames = 0;
    this.averageFPS = 0;
    this.minFPS = Infinity;
    this.maxFPS = 0;
    this.frameTimes = [];
    this.isWarning = false;
    this.isCritical = false;
  }

  updateFPS(currentFPS, currentTime = performance.now()) {
    this.totalFrames++;
    
    // Calculate frame time
    if (this.lastFrameTime > 0) {
      const frameTime = currentTime - this.lastFrameTime;
      this.frameTimes.push(frameTime);
      if (this.frameTimes.length > this.historySize) {
        this.frameTimes.shift();
      }
    }
    this.lastFrameTime = currentTime;
    
    // Update FPS history
    this.fpsHistory.push(currentFPS);
    if (this.fpsHistory.length > this.historySize) {
      this.fpsHistory.shift();
    }
    
    // Update statistics
    this.minFPS = Math.min(this.minFPS, currentFPS);
    this.maxFPS = Math.max(this.maxFPS, currentFPS);
    this.averageFPS = this.fpsHistory.reduce((sum, fps) => sum + fps, 0) / this.fpsHistory.length;
    
    // Check for frame drops
    const frameDropThreshold = this.targetFPS * this.frameDropThreshold;
    const warningThreshold = this.targetFPS * this.warningThreshold;
    
    if (currentFPS < frameDropThreshold) {
      this.frameDrops++;
      if (!this.isCritical) {
        this.isCritical = true;
        this.criticalStartTime = currentTime;
      }
    } else {
      if (this.isCritical) {
        this.isCritical = false;
      }
    }
    
    if (currentFPS < warningThreshold) {
      if (!this.isWarning) {
        this.isWarning = true;
        this.warningStartTime = currentTime;
      }
    } else {
      if (this.isWarning) {
        this.isWarning = false;
      }
    }
  }

  getPerformanceData() {
    const frameDropRate = this.totalFrames > 0 ? (this.frameDrops / this.totalFrames) * 100 : 0;
    const avgFrameTime = this.frameTimes.length > 0 
      ? this.frameTimes.reduce((sum, time) => sum + time, 0) / this.frameTimes.length 
      : 0;
    
    return {
      currentFPS: this.fpsHistory[this.fpsHistory.length - 1] || 0,
      averageFPS: Math.round(this.averageFPS * 10) / 10,
      minFPS: this.minFPS === Infinity ? 0 : this.minFPS,
      maxFPS: this.maxFPS,
      frameDrops: this.frameDrops,
      frameDropRate: Math.round(frameDropRate * 10) / 10,
      totalFrames: this.totalFrames,
      avgFrameTime: Math.round(avgFrameTime * 100) / 100,
      isWarning: this.isWarning,
      isCritical: this.isCritical,
      targetFPS: this.targetFPS
    };
  }

  getWarningMessage() {
    if (this.isCritical) {
      return `CRITICAL: Severe frame drops detected! FPS below ${Math.round(this.targetFPS * this.frameDropThreshold)}`;
    } else if (this.isWarning) {
      return `WARNING: Performance degraded. FPS below ${Math.round(this.targetFPS * this.warningThreshold)}`;
    }
    return null;
  }

  toggle() {
    this.isVisible = !this.isVisible;
  }

  show() {
    this.isVisible = true;
  }

  hide() {
    this.isVisible = false;
  }

  setPosition(position) {
    this.position = position;
  }
}
