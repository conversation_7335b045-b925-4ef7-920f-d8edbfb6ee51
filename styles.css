body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: #222;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

#game {
    position: relative;
    width: 800px;
    height: 600px;
    background: #333;
    border: 2px solid #555;
}

.game-board {
    position: relative;
    width: 100%;
    height: 100%;
}

.player {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #4CAF50;
    border-radius: 4px;
    transition: transform 0.1s ease;
}

.bomb {
    position: absolute;
    width: 24px;
    height: 24px;
    background: #FF5722;
    border-radius: 50%;
    animation: bomb-pulse 0.5s infinite alternate;
}

.block {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #8B4513;
    border: 1px solid #654321;
}

.wall {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #666;
    border: 1px solid #444;
}

.explosion {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #FFC107;
    animation: explosion 0.3s ease-out;
}

.hud {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 100;
}

.hud span {
    margin-right: 20px;
    font-weight: bold;
}

.hud-help {
    margin-top: 5px;
}

.hud-help small {
    color: #ccc;
    font-size: 10px;
}

@keyframes bomb-pulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

@keyframes explosion {
    0% { transform: scale(0.5); opacity: 1; }
    100% { transform: scale(1.5); opacity: 0; }
}

/* Performance Dashboard Styles */
.perf-dashboard {
    position: absolute;
    background: rgba(0, 0, 0, 0.85);
    border: 1px solid #555;
    border-radius: 6px;
    padding: 12px;
    font-family: 'Courier New', monospace;
    font-size: 12px;
    color: #fff;
    z-index: 200;
    min-width: 200px;
    backdrop-filter: blur(4px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.perf-dashboard--top-left {
    top: 10px;
    left: 10px;
}

.perf-dashboard--top-right {
    top: 10px;
    right: 10px;
}

.perf-dashboard--bottom-left {
    bottom: 10px;
    left: 10px;
}

.perf-dashboard--bottom-right {
    bottom: 10px;
    right: 10px;
}

.perf-dashboard--compact {
    padding: 8px;
    min-width: 120px;
}

.perf-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
    padding-bottom: 6px;
    border-bottom: 1px solid #444;
}

.perf-title {
    font-weight: bold;
    font-size: 13px;
}

.perf-status {
    font-size: 11px;
    font-weight: bold;
    text-transform: uppercase;
}

.perf-metrics {
    display: flex;
    flex-direction: column;
    gap: 4px;
}

.perf-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.perf-label {
    color: #ccc;
    font-size: 11px;
}

.perf-value {
    color: #fff;
    font-weight: bold;
    font-size: 12px;
}

.perf-value--primary {
    font-size: 14px;
}

.perf-warning {
    margin-top: 8px;
    padding: 6px 8px;
    border: 1px solid;
    border-radius: 4px;
    font-size: 11px;
    font-weight: bold;
    text-align: center;
    animation: perf-warning-pulse 1s infinite alternate;
}

.perf-toggle {
    position: absolute;
    top: 10px;
    left: 50%;
    transform: translateX(-50%);
    background: rgba(0, 0, 0, 0.7);
    border: 1px solid #555;
    border-radius: 4px;
    padding: 6px 12px;
    color: #fff;
    font-size: 12px;
    cursor: pointer;
    z-index: 150;
    transition: background-color 0.2s ease;
}

.perf-toggle:hover {
    background: rgba(0, 0, 0, 0.9);
    border-color: #777;
}

@keyframes perf-warning-pulse {
    0% { opacity: 0.8; }
    100% { opacity: 1; }
}