body {
    margin: 0;
    padding: 0;
    font-family: Arial, sans-serif;
    background: #222;
    color: white;
    display: flex;
    justify-content: center;
    align-items: center;
    min-height: 100vh;
}

#game {
    position: relative;
    width: 800px;
    height: 600px;
    background: #333;
    border: 2px solid #555;
}

.game-board {
    position: relative;
    width: 100%;
    height: 100%;
}

.player {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #4CAF50;
    border-radius: 4px;
    transition: transform 0.1s ease;
}

.bomb {
    position: absolute;
    width: 24px;
    height: 24px;
    background: #FF5722;
    border-radius: 50%;
    animation: bomb-pulse 0.5s infinite alternate;
}

.block {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #8B4513;
    border: 1px solid #654321;
}

.wall {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #666;
    border: 1px solid #444;
}

.explosion {
    position: absolute;
    width: 32px;
    height: 32px;
    background: #FFC107;
    animation: explosion 0.3s ease-out;
}

.hud {
    position: absolute;
    top: 10px;
    left: 10px;
    z-index: 100;
}

.hud span {
    margin-right: 20px;
    font-weight: bold;
}

.connection-indicator {
    display: inline-flex;
    align-items: center;
    gap: 5px;
    margin-left: 20px;
}

.status-dot {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666;
}

.status-dot--connected {
    background: #4CAF50;
    animation: pulse-green 2s infinite;
}

.status-dot--offline {
    background: #f44336;
}

@keyframes bomb-pulse {
    0% { transform: scale(1); }
    100% { transform: scale(1.1); }
}

@keyframes explosion {
    0% { transform: scale(0.5); opacity: 1; }
    100% { transform: scale(1.5); opacity: 0; }
}

/* Nickname Screen Styles */
.nickname-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(135deg, #1a1a1a 0%, #2d2d2d 100%);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1000;
}

.nickname-container {
    background: rgba(0, 0, 0, 0.8);
    border: 2px solid #555;
    border-radius: 12px;
    padding: 40px;
    max-width: 400px;
    width: 90%;
    text-align: center;
    box-shadow: 0 8px 32px rgba(0, 0, 0, 0.5);
    backdrop-filter: blur(10px);
}

.nickname-header {
    margin-bottom: 30px;
}

.game-title {
    font-size: 2.5em;
    margin: 0 0 10px 0;
    background: linear-gradient(45deg, #4CAF50, #8BC34A);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-weight: bold;
    text-shadow: 0 2px 4px rgba(0, 0, 0, 0.3);
}

.game-subtitle {
    color: #ccc;
    margin: 0;
    font-size: 1.1em;
}

.nickname-form {
    margin-bottom: 20px;
}

.input-group {
    margin-bottom: 20px;
    text-align: left;
}

.input-label {
    display: block;
    margin-bottom: 8px;
    color: #fff;
    font-weight: bold;
    font-size: 14px;
}

.nickname-input {
    width: 100%;
    padding: 12px 16px;
    border: 2px solid #555;
    border-radius: 8px;
    background: rgba(255, 255, 255, 0.1);
    color: #fff;
    font-size: 16px;
    transition: all 0.3s ease;
    box-sizing: border-box;
}

.nickname-input:focus {
    outline: none;
    border-color: #4CAF50;
    background: rgba(255, 255, 255, 0.15);
    box-shadow: 0 0 0 3px rgba(76, 175, 80, 0.2);
}

.nickname-input--error {
    border-color: #f44336;
    background: rgba(244, 67, 54, 0.1);
}

.nickname-input--error:focus {
    border-color: #f44336;
    box-shadow: 0 0 0 3px rgba(244, 67, 54, 0.2);
}

.error-message {
    color: #f44336;
    font-size: 12px;
    margin-top: 5px;
    text-align: left;
}

.nickname-requirements {
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
    padding: 12px;
    margin-bottom: 20px;
    text-align: left;
}

.nickname-requirements small {
    color: #aaa;
    font-weight: bold;
    display: block;
    margin-bottom: 5px;
}

.nickname-requirements ul {
    margin: 0;
    padding-left: 20px;
    color: #ccc;
}

.nickname-requirements li {
    font-size: 12px;
    margin-bottom: 2px;
}

.nickname-submit {
    width: 100%;
    padding: 14px 20px;
    background: linear-gradient(45deg, #4CAF50, #45a049);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: bold;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
}

.nickname-submit:hover:not(:disabled) {
    background: linear-gradient(45deg, #45a049, #4CAF50);
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(76, 175, 80, 0.3);
}

.nickname-submit:disabled {
    background: #666;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.nickname-submit--loading {
    background: #666;
}

.connection-status {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    margin: 20px 0;
    padding: 10px;
    background: rgba(255, 255, 255, 0.05);
    border-radius: 6px;
}

.status-indicator {
    width: 8px;
    height: 8px;
    border-radius: 50%;
    background: #666;
}

.status-indicator--connected {
    background: #4CAF50;
    animation: pulse-green 2s infinite;
}

.status-indicator--connecting {
    background: #FF9800;
    animation: pulse-orange 1s infinite;
}

.status-indicator--offline {
    background: #f44336;
}

.status-text {
    font-size: 12px;
    color: #ccc;
}

.nickname-footer {
    margin-top: 20px;
    padding-top: 20px;
    border-top: 1px solid #444;
}

.nickname-footer p {
    color: #aaa;
    margin: 0 0 10px 0;
    font-size: 14px;
}

.nickname-footer small {
    color: #666;
    font-size: 12px;
}

/* Loading Screen */
.loading-screen {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.9);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 1001;
}

.loading-container {
    text-align: center;
}

.loading-message {
    color: #fff;
    margin-top: 20px;
    font-size: 16px;
}

/* Loading Spinner */
.loading-spinner {
    display: inline-block;
    width: 16px;
    height: 16px;
    border: 2px solid #666;
    border-radius: 50%;
    border-top-color: #4CAF50;
    animation: spin 1s ease-in-out infinite;
}

.loading-spinner--large {
    width: 40px;
    height: 40px;
    border-width: 4px;
}

/* Animations */
@keyframes spin {
    to { transform: rotate(360deg); }
}

@keyframes pulse-green {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.5; }
}

@keyframes pulse-orange {
    0%, 100% { opacity: 1; }
    50% { opacity: 0.3; }
}