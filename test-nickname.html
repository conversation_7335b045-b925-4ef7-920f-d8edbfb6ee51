<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Nickname System</title>
    <style>
        body { font-family: Arial, sans-serif; padding: 20px; background: #222; color: white; }
        button { padding: 10px 20px; margin: 10px; background: #4CAF50; color: white; border: none; border-radius: 4px; cursor: pointer; }
        button:hover { background: #45a049; }
        .log { background: #333; padding: 10px; margin: 10px 0; border-radius: 4px; font-family: monospace; }
    </style>
</head>
<body>
    <h1>Bomberman DOM - Nickname System Test</h1>
    
    <div>
        <button onclick="testWebSocketConnection()">Test WebSocket Connection</button>
        <button onclick="testNicknameSubmission()">Test Nickname Submission</button>
        <button onclick="testInvalidNickname()">Test Invalid Nickname</button>
        <button onclick="clearLog()">Clear Log</button>
    </div>
    
    <div id="log"></div>
    
    <script>
        function log(message) {
            const logDiv = document.getElementById('log');
            const entry = document.createElement('div');
            entry.className = 'log';
            entry.textContent = new Date().toLocaleTimeString() + ': ' + message;
            logDiv.appendChild(entry);
            console.log(message);
        }
        
        function clearLog() {
            document.getElementById('log').innerHTML = '';
        }
        
        let ws = null;
        
        function testWebSocketConnection() {
            log('Testing WebSocket connection to ws://localhost:3000...');
            
            try {
                ws = new WebSocket('ws://localhost:3000');
                
                ws.onopen = () => {
                    log('✅ WebSocket connected successfully!');
                };
                
                ws.onmessage = (event) => {
                    const message = JSON.parse(event.data);
                    log('📨 Received: ' + JSON.stringify(message, null, 2));
                };
                
                ws.onclose = () => {
                    log('🔌 WebSocket connection closed');
                };
                
                ws.onerror = (error) => {
                    log('❌ WebSocket error: ' + error);
                };
                
            } catch (error) {
                log('❌ Failed to create WebSocket: ' + error.message);
            }
        }
        
        function testNicknameSubmission() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket not connected. Please test connection first.');
                return;
            }
            
            const nickname = 'TestPlayer' + Math.floor(Math.random() * 1000);
            log('🚀 Testing nickname submission: ' + nickname);
            
            const message = {
                type: 'set_nickname',
                nickname: nickname,
                timestamp: Date.now()
            };
            
            ws.send(JSON.stringify(message));
            log('📤 Sent nickname request: ' + JSON.stringify(message));
        }
        
        function testInvalidNickname() {
            if (!ws || ws.readyState !== WebSocket.OPEN) {
                log('❌ WebSocket not connected. Please test connection first.');
                return;
            }
            
            const invalidNickname = 'a'; // Too short
            log('🚀 Testing invalid nickname: ' + invalidNickname);
            
            const message = {
                type: 'set_nickname',
                nickname: invalidNickname,
                timestamp: Date.now()
            };
            
            ws.send(JSON.stringify(message));
            log('📤 Sent invalid nickname request: ' + JSON.stringify(message));
        }
        
        // Auto-start connection test
        log('🎮 Bomberman DOM Nickname System Test Ready');
        log('Click "Test WebSocket Connection" to start testing');
    </script>
</body>
</html>
