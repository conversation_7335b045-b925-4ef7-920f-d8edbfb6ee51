<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Bomberman DOM - Test</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div id="game"></div>
    <script type="module">
        // Test script to check if everything loads
        console.log('Starting Bomberman DOM test...');
        
        try {
            import('./src/game.js').then(() => {
                console.log('✅ Game module loaded successfully');
                console.log('✅ Application should be running');
                console.log('✅ Check the page for the nickname input screen');
            }).catch(error => {
                console.error('❌ Error loading game module:', error);
            });
        } catch (error) {
            console.error('❌ Error in test script:', error);
        }
    </script>
</body>
</html>
